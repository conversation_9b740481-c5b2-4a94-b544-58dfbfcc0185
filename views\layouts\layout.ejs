<!DOCTYPE html>
<html lang="pt-br" data-bs-theme="light">
  <head>
      <meta charset="utf-8">
      <meta content="width=device-width, initial-scale=1.0" name="viewport">
    
      <title>Alunos-Aurora Boreal</title>
      <meta content="" name="description">
      <meta content="" name="keywords">
      <!-- Bootstrap CSS -->
      <link href="/static/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    
      <!-- Favicons -->
      <link href="/static/img/AURORALOGOSFUNDO.ico" rel="icon">
      <link href="/static/img/AURORALOGOSFUNDO.ico" rel="apple-touch-icon">
    
      <!-- Google Fonts -->
      <link href="https://fonts.gstatic.com" rel="preconnect">
      <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Nunito:300,300i,400,400i,600,600i,700,700i|Poppins:300,300i,400,400i,500,500i,600,600i,700,700i" rel="stylesheet">
    
      <!-- Vendor CSS Files -->
      <link href="/static/assets/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
      <link href="/static/assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
      <link href="/static/assets/vendor/boxicons/css/boxicons.min.css" rel="stylesheet">
      <link href="/static/assets/vendor/quill/quill.snow.css" rel="stylesheet">
      <link href="/static/assets/vendor/quill/quill.bubble.css" rel="stylesheet">
      <link href="/static/assets/vendor/remixicon/remixicon.css" rel="stylesheet">
      <link href="/static/assets/vendor/simple-datatables/style.css" rel="stylesheet">
    
      <!-- Template Main CSS File -->
      <link href="/static/assets/css/style.css" rel="stylesheet">

      <!-- Base URL -->
      <base href="/system/">

      <style>
        #header{
          background: rgb(4,196,0);
          background: linear-gradient(90deg, rgba(4,196,0,1) 0%, rgba(9,17,121,1) 79%, rgba(154,0,255,1) 100%); 
        }
      </style>

      <div vw="" class="enabled" style="left: initial; right: 0px; top: 50%; bottom: initial; transform: translateY(calc(-50% - 10px));">
      <div vw-access-button="" class="active"><img class="access-button" data-src="assets/access_icon.svg" alt="Conteúdo acessível em Libras usando o VLibras Widget com opções dos Avatares Ícaro, Hosana ou Guga." src="https://vlibras.gov.br/app//assets/access_icon.svg">
      <img class="pop-up" data-src="assets/access_popup.jpg" alt="Conteúdo acessível em Libras usando o VLibras Widget com opções dos Avatares Ícaro, Hosana ou Guga." src="https://vlibras.gov.br/app//assets/access_popup.jpg"></div>
      <div vw-plugin-wrapper=""><div vp="">
      <div vp-box=""></div>
      <div vp-message-box=""></div>
      <div vp-settings=""></div>
      <div vp-dictionary=""></div>
      <div vp-settings-btn=""></div>
      <div vp-info-screen=""></div>
      <div vp-suggestion-screen=""></div>
      <div vp-translator-screen=""></div>
      <div vp-main-guide-screen=""></div>
      <div vp-suggestion-button=""></div>
      <div vp-rate-box=""></div>
      <div vp-change-avatar=""></div>
      <div vp-additional-options=""></div>
      <div vp-controls=""></div>
      <span vp-click-blocker=""></span>
      </div></div>
      </div>

      <!-- =======================================================
      * Template Name: NiceAdmin
      * Template URL: https://bootstrapmade.com/nice-admin-bootstrap-admin-html-template/
      * Updated: Apr 20 2024 with Bootstrap v5.3.3
      * Author: BootstrapMade.com
      * License: https://bootstrapmade.com/license/
      ======================================================== -->
    </head>

  <body>
      <!-- ======= Header ======= -->
    <header id="header" class="header fixed-top d-flex align-items-center">

      <div class="d-flex align-items-center justify-content-between">
        <a href="/" class="logo d-flex align-items-center">
          <img src="img/AURORALOGOSFUNDO.ico" alt="">
          <span class="d-none d-lg-block">Aurora Boreal</span>
        </a>
        <i class="bi bi-list toggle-sidebar-btn"></i>
      </div><!-- End Logo -->




      </ul>
      </nav><!-- End Icons Navigation -->

    </header><!-- End Header -->
      <aside id="sidebar" class="sidebar">

        <div class="profile">
          <div class="profile-img">
            <img src='/img/AURORALOGOSFUNDO.ico' onerror="this.src='/img/AURORALOGOSFUNDO.ico'">
          </div>
          <h3 id="NomeUsu"></h3>
          <p id="PerfilUsu"></p>
        </div>

        <ul class="sidebar-nav" id="sidebar-nav">
          <li class="nav-item">
            <a class="nav-link collapsed" data-bs-target="#students-nav" data-bs-toggle="collapse" href="#">
              <i class="bi bi-people"></i><span>Gerenciar Alunos</span><i class="bi bi-chevron-down ms-auto"></i>
            </a>
            <ul id="students-nav" class="nav-content collapse " data-bs-parent="#sidebar-nav">
              <li>
                <a href="/system/direcao/alunos/atividades">
                  <i class="bi bi-circle"></i><span>Cadastro e Listagem de Alunos</span>
                </a>
              </li>
            </ul>
          </li>

        </ul>

    </aside>
    <!-- End Sidebar-->
    <%- body %>
  </body>
      <!-- Bootstrap JS Files -->
      <script src="/static/js/jquery-3.2.1.slim.min.js" integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN" crossorigin="anonymous"></script>
      <script src="/static/js/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
      <script src="/static/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

      <script>
        function yes(){
          let usuNome = req.cookies.usuarioLogadoNome;
          let usuperfil = req.cookies.usuarioLogadoDesc;

          let nome = document.querySelector("#NomeUsu");
          let perfil = document.querySelector("#perfilUsu");

          nome.innerHTML = usuNome;
          perfil.innerHTML = usuperfil;
        }
        document.addEventListener("DOMContentLoaded", (event) => {
          yes();
        });

      </script>

      <footer id="footer" class="footer">
      <div class="copyright">
        &copy; Copyright <strong><span>NiceAdmin</span></strong>. All Rights Reserved
        &copy; Copyright <strong><span>Devminds</span></strong>. All Rights Reserved
        &copy; Copyright <strong><span>Aurora Boreal</span></strong>. All Rights Reserved
      </div>

    </footer><!-- End Footer -->

    <a href="#" class="back-to-top d-flex align-items-center justify-content-center"><i
        class="bi bi-arrow-up-short"></i></a>

    <!-- Vendor JS Files -->
    <script src="assets/vendor/apexcharts/apexcharts.min.js"></script>
    <script src="assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="assets/vendor/chart.js/chart.umd.js"></script>
    <script src="assets/vendor/echarts/echarts.min.js"></script>
    <script src="assets/vendor/quill/quill.js"></script>
    <script src="assets/vendor/simple-datatables/simple-datatables.js"></script>
    <script src="assets/vendor/tinymce/tinymce.min.js"></script>
    <script src="assets/vendor/php-email-form/validate.js"></script>

    <!-- Template Main JS File -->
    <script src="assets/js/main.js"></script>
    <script src="https://cdn.sheetjs.com/xlsx-0.20.3/package/dist/xlsx.full.min.js"></script>
    </body>
</html>
